<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
  <rect x="50" y="50" width="300" height="200" fill="#fff7e6" stroke="#fa8c16" stroke-width="2" rx="10"/>
  <text x="200" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#fa8c16">TEST001</text>
  <text x="200" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#666">内部结构图</text>
  <text x="200" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#999">Internal Structure</text>
  
  <!-- 模拟内部结构 -->
  <rect x="80" y="80" width="240" height="140" fill="#fff" stroke="#ddd" stroke-width="1" rx="5"/>
  
  <!-- CPU芯片 -->
  <rect x="100" y="100" width="60" height="40" fill="#722ed1" rx="2"/>
  <text x="130" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#fff">CPU</text>
  
  <!-- 内存 -->
  <rect x="180" y="100" width="40" height="60" fill="#13c2c2" rx="2"/>
  <text x="200" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#fff">RAM</text>
  
  <!-- 存储 -->
  <rect x="240" y="100" width="60" height="40" fill="#52c41a" rx="2"/>
  <text x="270" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#fff">FLASH</text>
  
  <!-- 连接线 -->
  <line x1="160" y1="120" x2="180" y2="120" stroke="#666" stroke-width="2"/>
  <line x1="220" y1="120" x2="240" y2="120" stroke="#666" stroke-width="2"/>
  
  <!-- 接口 -->
  <rect x="100" y="170" width="200" height="20" fill="#f5222d" rx="2"/>
  <text x="200" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#fff">I/O INTERFACE</text>
</svg>
