<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片查看功能测试</title>
    <link rel="stylesheet" href="static/style.css">
    <link rel="stylesheet" href="static/page_js_css/ProductTestQuery.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .sn-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin-right: 10px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>产品图片查看功能测试</h1>
        
        <div class="test-section">
            <h3>测试说明</h3>
            <p>此页面用于测试产品测试查询页面中的图片查看功能。</p>
            <p>功能包括：</p>
            <ul>
                <li>通过SN号获取产品图片列表</li>
                <li>图片画廊展示</li>
                <li>图片预览功能</li>
                <li>响应式设计</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>功能测试</h3>
            <div>
                <input type="text" class="sn-input" id="test-sn" placeholder="输入SN号" value="TEST001">
                <button class="test-button" onclick="testShowProductImages()">
                    <i class="fas fa-images"></i> 测试图片查看
                </button>
            </div>
            <p style="margin-top: 10px; color: #666; font-size: 14px;">
                注意：当前使用模拟数据进行测试
            </p>
        </div>

        <div class="test-section">
            <h3>API测试</h3>
            <button class="test-button" onclick="testImageAPI()">
                <i class="fas fa-code"></i> 测试API接口
            </button>
            <div id="api-result" style="margin-top: 15px; padding: 10px; background: #f8f8f8; border-radius: 4px; display: none;">
                <h4>API响应结果：</h4>
                <pre id="api-response" style="white-space: pre-wrap; word-wrap: break-word;"></pre>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="static/js/utils/logger.js"></script>
    <script src="static/js/utils/toast.js"></script>
    <script src="static/page_js_css/ProductTestQuery.js"></script>

    <script>
        // 测试图片查看功能
        function testShowProductImages() {
            const sn = document.getElementById('test-sn').value.trim();
            if (!sn) {
                alert('请输入SN号');
                return;
            }
            
            // 调用ProductTestQuery.js中的图片查看功能
            if (typeof window.showProductImages === 'function') {
                window.showProductImages(sn);
            } else {
                alert('图片查看功能未加载');
            }
        }

        // 测试API接口
        async function testImageAPI() {
            const sn = document.getElementById('test-sn').value.trim() || 'TEST001';
            const resultDiv = document.getElementById('api-result');
            const responseDiv = document.getElementById('api-response');
            
            try {
                resultDiv.style.display = 'block';
                responseDiv.textContent = '正在请求API...';
                
                const response = await fetch(`/api/product-test-query/images/${encodeURIComponent(sn)}`);
                const result = await response.json();
                
                responseDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                responseDiv.textContent = `API请求失败: ${error.message}`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('图片查看功能测试页面已加载');
            
            // 检查必要的函数是否已加载
            if (typeof window.showProductImages === 'function') {
                console.log('✓ showProductImages 函数已加载');
            } else {
                console.error('✗ showProductImages 函数未加载');
            }
            
            if (typeof window.showToast === 'function') {
                console.log('✓ showToast 函数已加载');
            } else {
                console.error('✗ showToast 函数未加载');
            }
        });
    </script>
</body>
</html>
